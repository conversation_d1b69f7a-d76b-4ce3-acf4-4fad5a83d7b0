use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// User model
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub user_id: String, // Firebase user ID
    pub email: String,
    pub display_name: Option<String>,
    pub photo_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
}

/// New user request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewUser {
    pub user_id: String, // Firebase user ID
    pub email: String,
    pub display_name: Option<String>,
    pub photo_url: Option<String>,
}

/// Update user request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUser {
    pub email: Option<String>,
    pub display_name: Option<String>,
    pub photo_url: Option<String>,
    pub last_login: Option<DateTime<Utc>>,
}

/// User response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: String,
    pub user_id: String,
    pub email: String,
    pub display_name: Option<String>,
    pub photo_url: Option<String>,
    pub created_at: String,
    pub updated_at: String,
    pub last_login: Option<String>,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        Self {
            id: user.id.to_string(),
            user_id: user.user_id,
            email: user.email,
            display_name: user.display_name,
            photo_url: user.photo_url,
            created_at: user.created_at.to_rfc3339(),
            updated_at: user.updated_at.to_rfc3339(),
            last_login: user.last_login.map(|dt| dt.to_rfc3339()),
        }
    }
}
