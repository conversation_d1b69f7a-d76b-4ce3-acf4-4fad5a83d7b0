import { getAuth } from 'firebase/auth';
import type { Task, TaskUpdateResponse } from '@/types';

/**
 * Connect to the Ki server using the Firebase ID token
 * This will establish a session with the Ki server
 * @returns Promise<string> User ID if successful
 */
export async function connectToKiServer(server: string): Promise<string> {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
        throw new Error('User not authenticated');
    }

    // Get the Firebase ID token
    const idToken = await user.getIdToken();

    // Send the token to the Ki server
    const response = await fetch(`${server}/connect`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${idToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to connect to Ki server: ${errorText}`);
    }

    return await response.text();
}

/**
 * Create a new project on the Ki server
 * @param name Project name
 * @param description Project description (optional)
 * @returns Promise<any> Created project
 */
// export async function createProject(server: string, name: string, description?: string): Promise<any> {
//     const response = await fetch(`${server}/projects`, {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json',
//             'Accept': 'application/json'
//         },
//         credentials: 'include',
//         mode: 'cors',
//         body: JSON.stringify({
//             name,
//             description
//         })
//     });

//     if (!response.ok) {
//         const errorText = await response.text();
//         throw new Error(`Failed to create project: ${errorText}`);
//     }

//     return await response.json();
// }

/**
 * Get all tasks from the Ki server
 * @param projectId Project ID
 * @returns Promise<any[]> Array of tasks
 */
export async function getAllTasks(server: string): Promise<Task[]> {
    const response = await fetch(`${server}/tasks`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get tasks: ${errorText}`);
    }

    return await response.json();
}

/**
 * Create a new task on the Ki server
 * @param projectId Project ID
 * @param task Task data
 * @returns Promise<any> Created task
 */
export async function createTask(server: string, task: Omit<Task, 'id'>): Promise<any> {
    const response = await fetch(`${server}/tasks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(task)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create task: ${errorText}`);
    }

    return await response.json();
}



/**
 * Update a task on the Ki server
 * @param taskId Task ID
 * @param task Task data to update
 * @returns Promise<TaskUpdateResponse> Updated task and affected tasks
 */
export async function updateTask(server: string, task: Task): Promise<TaskUpdateResponse> {
    const response = await fetch(`${server}/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(task)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update task: ${errorText}`);
    }

    return await response.json();
}

/**
 * Delete a task from the Ki server
 * @param taskId Task ID
 * @returns Promise<void>
 */
export async function deleteTask(server: string, taskId: string): Promise<void> {
    const response = await fetch(`${server}/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete task: ${errorText}`);
    }
}

/**
 * Move a task to a different status and/or position
 * @param taskId Task ID
 * @param status New status
 * @param position New position
 * @returns Promise<any> Updated task
 */
export async function moveTask(server: string, taskId: string, status: string, position: number): Promise<any> {
    const response = await fetch(`${server}/tasks/${taskId}/move`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify({
            status,
            position
        })
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to move task: ${errorText}`);
    }

    return await response.json();
}
