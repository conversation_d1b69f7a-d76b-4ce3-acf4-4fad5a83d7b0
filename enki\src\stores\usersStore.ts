import { defineStore } from "pinia";
import { ref } from "vue";

// Placeholder for users
const users = ref([
    { id: 'user1', name: '<PERSON> P.' },
    { id: 'user2', name: '<PERSON>' },
    { id: 'user3', name: '<PERSON>' },
]);

export const useUsersStore = defineStore('users', {
    state: () => ({
        users
    }),
    getters: {
        getUser:(state) => (id: string) => {
            return state.users.find(user => user.id === id);
        }
    }
});