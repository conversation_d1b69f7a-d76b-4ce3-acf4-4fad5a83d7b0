import { defineStore } from "pinia";
import { ref } from "vue";
import type { User } from "@/types";
import { getAllUsers, createUser, updateUser, deleteUser } from "@/services/kiApi";
import { useSpacesStore } from "./spacesStore";

export const useUsersStore = defineStore('users', {
    state: () => ({
        users: ref<User[]>([]),
        loading: ref(false),
        error: ref<string | null>(null)
    }),
    getters: {
        getUser: (state) => (id: string) => {
            return state.users.find(user => user.id === id);
        },
        getUserByFirebaseId: (state) => (firebase_user_id: string) => {
            return state.users.find(user => user.user_id === firebase_user_id);
        }
    },
    actions: {
        async fetchUsers() {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                const users = await getAllUsers(server);
                // Add computed name property for backward compatibility
                this.users = users.map(user => ({
                    ...user,
                    name: user.display_name || user.email
                }));
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to fetch users';
                console.error('Error fetching users:', error);
            } finally {
                this.loading = false;
            }
        },

        async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                const newUser = await createUser(server, userData);
                const userWithName = {
                    ...newUser,
                    name: newUser.display_name || newUser.email
                };
                this.users.push(userWithName);
                return userWithName;
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to create user';
                console.error('Error creating user:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async updateUser(id: string, userData: Partial<User>) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                const updatedUser = await updateUser(server, { id, ...userData } as User);
                const userWithName = {
                    ...updatedUser,
                    name: updatedUser.display_name || updatedUser.email
                };
                const index = this.users.findIndex(user => user.id === id);
                if (index !== -1) {
                    this.users[index] = userWithName;
                }
                return userWithName;
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to update user';
                console.error('Error updating user:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async deleteUser(id: string) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                await deleteUser(server, id);
                const index = this.users.findIndex(user => user.id === id);
                if (index !== -1) {
                    this.users.splice(index, 1);
                }
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to delete user';
                console.error('Error deleting user:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        }
    }
});